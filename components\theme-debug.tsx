"use client";

import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

export function ThemeDebug() {
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="fixed top-20 right-4 z-50 p-2 bg-blue-500 text-white text-xs rounded">
        Loading theme...
      </div>
    );
  }

  return (
    <div className="fixed top-20 right-4 z-50 p-2 bg-green-500 text-white text-xs rounded">
      Theme: {theme} | Resolved: {resolvedTheme}
    </div>
  );
}
